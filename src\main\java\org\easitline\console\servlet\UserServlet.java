package org.easitline.console.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.utils.AccountGenerator;
import org.easitline.console.utils.AesUtils;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/servlet/user/*")
public class UserServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;

	
	public void editPwd() {
		int changePwdlimitDay = Integer.valueOf(appContext.getProperty("changePwdlimitDay", "0"));
		this.getRequest().setAttribute("changePwdlimitDay", changePwdlimitDay);
		renderJsp("/pages/server/pwd-edit.jsp");
	}
	
	public EasyResult actionForGetUser() {
		if(!hasOperateAuth()) {
			return EasyResult.fail();
		}
		try {
			EasySQL sql = new EasySQL();
			sql.append(getJsonPara("userId"),"select * from easi_user where user_id = ?");
			JSONObject row = this.getConsoleQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			return EasyResult.ok(row);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForUserList() {
		if(!hasOperateAuth()) {
			return EasyResult.fail();
		}
		 try {
			EasySQL sql = new EasySQL();
			sql.append("select * from easi_user");
			List<JSONObject> list = this.getConsoleQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			return EasyResult.ok(list);
		 } catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForAddUser() {
		if(!hasOperateAuth()) {
			return EasyResult.fail();
		}
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("EASI_USER","USER_ID");
		try {
			String userAcct = params.getString("userAcct");
			if(userAcct.length()<6) {
				return EasyResult.fail("账号长度不能少于6位");
			}
			String pwd = AccountGenerator.generateSecurePassword();
			this.info("pwd>"+pwd, null);
			String _pwd = "AES_"+AesUtils.encrypt(pwd, AesUtils.USER_KEY);
			record.set("USER_ID", RandomKit.uuid());
			record.set("LOGIN_ACCT", userAcct);
			record.set("ROLE_ID", params.getString("roleId"));
			record.set("MOBILE", params.getString("mobile"));
			record.set("STATE", params.getString("userState"));
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("LOGIN_PWD",_pwd);
			this.getConsoleQuery().save(record);
			addOperateLog("新增账号："+userAcct, null);
			return EasyResult.ok(pwd);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForUpdateUser() {
		if(!hasOperateAuth()) {
			return EasyResult.fail();
		}
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("EASI_USER","USER_ID");
		try {
			if(StringUtils.isBlank(params.getString("userId"))) {
				record.setPrimaryKey("LOGIN_ACCT");
				record.set("USER_ID",  RandomKit.uniqueStr());
			}else {
				record.set("USER_ID",  params.getString("userId"));
			}
			String userAcct = params.getString("userAcct");
			record.set("LOGIN_ACCT", userAcct);
			record.set("ROLE_ID", params.getString("roleId"));
			record.set("STATE", params.getString("userState"));
			record.set("MOBILE", params.getString("mobile"));
			this.getConsoleQuery().update(record);
			addOperateLog("修改账号："+userAcct, null);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForDelUser() {
		if(!hasOperateAuth()) {
			return EasyResult.fail();
		}
		JSONObject params = getJSONObject();
		String userId = params.getString("userId");
		try {
			int count = this.getConsoleQuery().queryForInt("select count(1) from easi_user where state = 0");
			if(count<=1) {
				return EasyResult.fail("至少保留一个可用的账号！");
			}
			int result = this.getConsoleQuery().executeUpdate("delete from easi_user where user_id = ?", userId);
			if(result==0) {
				return EasyResult.fail("删除失败！");
			}
			addOperateLog("修改账号", userId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForResetUserPwd() {
		if(!hasOperateAuth()) {
			return EasyResult.fail();
		}
		JSONObject params = getJSONObject();
		String userId = params.getString("userId");
		try {
			String pwd = AccountGenerator.generateSecurePassword();
			this.info("pwd>"+pwd,null);
			String _pwd = "AES_"+AesUtils.encrypt(pwd, AesUtils.USER_KEY);
			this.getConsoleQuery().executeUpdate("update easi_user set LOGIN_PWD = ? where USER_ID = ?",_pwd,userId);
			addOperateLog("重置密码", userId);
			return EasyResult.ok(pwd);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
    
	public EasyResult actionForModifyLoginInfo(){
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		data = data.replaceAll("\"", "'");
		
		JSONObject jsonObject = JsonKit.getJSONObject(data,null);
		
		String acct = (String)this.getRequest().getSession(false).getAttribute("MARS_CONSOLE_USER");
		String newacct =StringUtils.isBlank(jsonObject.getString("acct"))?acct:jsonObject.getString("acct");
		String oldpwd = jsonObject.getString("oldpwd");
		String newpwd = jsonObject.getString("newpwd");
		if(StringUtils.isBlank(newpwd)||StringUtils.isBlank(oldpwd)) {
			return EasyResult.fail("密码不能为空");
		}
		if(!validatePassword(newpwd)) {
			return EasyResult.fail("密码不能是纯数字或字母，需数字和字母组合或和符号，长度大于8位!");
		}
		
		String sql  = "select LOGIN_PWD from EASI_USER  WHERE LOGIN_ACCT = ?";
		try{
			String pwd = this.getConsoleQuery().queryForString(sql, new Object[]{newacct});
			if(StringUtils.isBlank(pwd)) {
				return EasyResult.fail("账号【"+newacct+"】不存在");
			}
			if(pwd.startsWith("AES")){
				pwd = pwd.replace("AES_", "");
				pwd = AesUtils.decrypt(pwd, AesUtils.USER_KEY);
			}
			if(!pwd.equalsIgnoreCase(oldpwd)){
				return EasyResult.error(500,"当前账号或密码错误！");
			}
		}catch(Exception ex){
			this.error(ex.getMessage(), ex);
			return EasyResult.error(500,"当前账号或密码错误");
		}
		
		sql = "update EASI_USER  set LOGIN_ACCT = ? , LOGIN_PWD = ?, LAST_PWD_CHANGE_TIME = ? where LOGIN_ACCT = ? ";
		
		this.addOperateLog("修改密码"+newacct,null);
		
		try{
			 newpwd = "AES_"+AesUtils.encrypt(newpwd, AesUtils.USER_KEY);
			 this.getConsoleQuery().executeUpdate(sql, new Object[]{newacct,newpwd,EasyDate.getCurrentDateString(),acct});
			 return EasyResult.ok(null,"保存成功，请重新登录！");
		}catch(Exception ex){
			this.error(ex.getMessage(), ex);
			return EasyResult.error(500,"当前账号或密码错误！");
		}
		
	}
	
	public static boolean validatePassword(String password) {
	    if (password.matches("\\d+") || password.matches("[a-zA-Z]+")) {
	        return false;
	    }
	    if (password.length() < 8) {
	        return false;
	    }
	    return true;
	}
	
}
