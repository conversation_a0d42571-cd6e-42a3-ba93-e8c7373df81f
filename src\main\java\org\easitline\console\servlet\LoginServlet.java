package org.easitline.console.servlet;


import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.EasyServer;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.RandomKit.SMSAuthCodeType;
import org.easitline.common.utils.kit.VerifyCodeKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.base.Constants;
import org.easitline.console.utils.AccountGenerator;
import org.easitline.console.utils.AesUtils;
import org.easitline.console.utils.LoginSessionUtils;
import org.easitline.console.utils.MachineCodeGenerator;
import org.easitline.console.utils.PropKit;
import org.easitline.console.utils.ScriptHelper;
import org.easitline.console.utils.StringFilter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;


/**
 * 用户登录
 */
@WebServlet(urlPatterns = { "/login/*","/sso/*"})
public class LoginServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;
    
	private static Map<String,Integer> loginErrorInfo = new HashMap<String, Integer>();
	private static Map<String,Long> loginTimeInfo = new HashMap<String, Long>();
	
	public void actionForIndex() {
		this.info(WebKit.getIP(getRequest())+">access console>"+getRequest().getRequestURL().toString(),null);
		
//		boolean validationMachineCodeResult = validationMachineCode();
//		if(!validationMachineCodeResult) {
//			return;
//		}
		
		setAttr("platform", ServerContext.getServerName()+"&"+ServerContext.getNodeName());
		setAttr("profileActive", ServerContext.getProperties("PROFILE_ACTIVE",""));
		setAttr("versionInfo", "Mars"+EasyServer.getVersion()+"_"+EasyServer.getVersionDate());
		
		
		String consoleAllocAccessIp = ServerContext.getProperties("CONSOLE_ALLOC_ACCESS_IP","");
		if(StringUtils.isBlank(consoleAllocAccessIp)) {
			//consoleAllocAccessIp = appContext.getProperty("ip","");
		}
		if(StringUtils.isNotBlank(consoleAllocAccessIp)) {
			String ip = WebKit.getIP(getRequest());
			boolean accessAuth = false;
			String[] ips = consoleAllocAccessIp.split(",");
			for(String _ip:ips) {
				if(ip.startsWith(_ip)) {
					accessAuth = true;
					break;
				}
			}
			if(!accessAuth) {
				info(ip+"不在配置的【"+consoleAllocAccessIp+"】", null);
//				renderHtml("ip受限");
				try {
					getResponse().sendError(404);
				} catch (IOException e) {
					this.error(e.getMessage(), e);
//					renderHtml("ip受限");
				}
				return;
			}
		}
		
		String allocAccessServerAddr = ServerContext.getProperties("ALLOC_ACCESS_SERVER_ADDR","");
		if(StringUtils.isBlank(allocAccessServerAddr)) {
			//allocAccessServerAddr = appContext.getProperty("allocAccessIp","");
		}
		//只允许内网访问
		if(StringUtils.isNotBlank(allocAccessServerAddr)) {
			String accessUrl = getRequest().getRequestURL().toString();
			boolean accessAuth = false;
			String[] ips = allocAccessServerAddr.split(",");
			for(String ip:ips) {
				if(accessUrl.indexOf(ip)>-1) {
					accessAuth = true;
					break;
				}
			}
			if(!accessAuth) {
//				renderHtml("不允许从未配置的来源访问.");
				this.warn(accessUrl+">只允许内网访问（"+allocAccessServerAddr+"）", null);
				try {
					getResponse().sendError(404);
				} catch (IOException e) {
					e.printStackTrace();
					renderHtml("不允许从未配置的来源访问.");
				}
				return;
			}
		}
		
		setAttr("prefixPath", Constants.getContextPrefix());
		
		//是否需要短信校验登录
		setAttr("smsLoginVerify", smsLoginVerify()?"1":"0");
		setAttr("secondCodeCheck",StringUtils.isBlank(System.getProperty("secondCode", ""))?"0":"1");
		
		String key  = ServerContext.getProperties("SECURITY_KEY", "");
		String flag = ServerContext.getProperties("SECURITY_ENTER", "");
		String securityEnter = PropKit.get("SECURITY_ENTER", "0");
		
		if(("1".equals(flag)||"1".equals(securityEnter))&&StringUtils.isNotBlank(key)) {
			String _key = getPara("key");
			if(StringUtils.isBlank(_key)) {
				this.warn("安全KEY不能为空",null);
				renderHtml("in empty.");
				return;
			}
			if(!_key.equals(key)) {
				this.warn("安全KEY错误",null);
				renderHtml("in error");
				return;
			}
			String url  = getRequest().getRequestURL().toString()+"?"+getRequest().getQueryString();
			if(url.indexOf("key")>-1) {
				renderJsp("/WEB-INF/pages/login.jsp");
				return;
			}else {
				this.warn("请从安全入口访问.", null);
				renderHtml("Please access it from the security portal");
				return;
			}
		}
		renderJsp("/WEB-INF/pages/login.jsp");
	}
	
	public boolean  validationMachineCode() {
		ScriptHelper.addMachineCode(true);
		String nowMachineCode = MachineCodeGenerator.generateMachineCode();
		String machineCode = ServerContext.getProperties("MACHINE_CODE", "0");
		boolean result =  nowMachineCode.equalsIgnoreCase(machineCode);
		if(!result) {
			this.error("当前机器码："+nowMachineCode+",和配置不一致，请初始化",null);
		}
		return true;
	}

	public String actionForLogout(){
		LoginSessionUtils.removeLoginInfo(this.getRequest().getSession().getId());
		this.getRequest().getSession().removeAttribute("MARS_CONSOLE_USER");
		this.getRequest().getSession().removeAttribute("MARS_CONSOLE_ROLE");
		return "/index.jsp";
	}
	
	public EasyResult actionForGetKey(){
		return EasyResult.ok("mLHgjzdkhkxAFY6f");
	}
		
	public EasyResult actionForLogin(){
		EasyResult  result = new EasyResult();
		if(!this.getMothed().equals("post")) {  //这里只允许采用http post的方式访问
			result.addFail("非法访问");
			return result;
		}
		JSONObject jsonObject=getJSONObject();
		this.info("login data>>>>>"+JSON.toJSONString(jsonObject), null);
		
		
		EasyResult loginAuth = loginAuth(jsonObject.getString("j_imagecode"));
		if(!loginAuth.isOk()) {
			return loginAuth;
		}		
		String secondCode = jsonObject.getString("secondCode");
		String _secondCode = System.getProperty("secondCode", "");
		if(!StringUtils.isBlank(_secondCode)) {
			if(StringUtils.isBlank(secondCode)) {
				result.addFail("二次校验码不能为空");
				return result;
			}
		    if(!_secondCode.equals(secondCode)) {
		    	result.addFail("二次校验码错误！");
				return result;
		    }
		}
		
		
		String ip=WebKit.getIP(getRequest());
	
		EasyRecord record = new EasyRecord("EASI_LOGIN_LOG","LOG_ID");
		
		this.getRequest().getSession().removeAttribute("randyString");
		String  username = StringUtils.trimToEmpty(jsonObject.getString("j_username"));
		String  password = StringUtils.trimToEmpty(jsonObject.getString("j_password"));
		if("".equals(username) || "".equals(password) ){
			this.error("j_username or j_password is empty", null);
			result.addFail("账号或密码错误！");
			return result;
		}
		
		String gLoginAuth = ServerContext.getProperties("G_LOGIN_AUTH","true");
		if("true".equalsIgnoreCase(gLoginAuth)&&"admin@console".equals(username)){
			result.addFail("该账号禁止登录！");
			return result;
		}
		
		String loginAccountSource = appContext.getProperty("loginAccountSource", "0");
		
		if("0".equals(loginAccountSource)) {
			if("admin".equals(username)) {
				String sql="select count(1) from EASI_USER WHERE LOGIN_ACCT = ?";
				try {
					boolean hasAcount = this.getConsoleQuery().queryForExist(sql, "admin@mars");
					if(hasAcount) {
						return EasyResult.fail("该账号不允许登录");
					}
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
			String sql="select count(1) from EASI_USER WHERE LOGIN_ACCT = ?  and STATE = 0";
			try {
				boolean hasAcount = this.getConsoleQuery().queryForExist(sql,username);
				if(!hasAcount) {
					return EasyResult.fail("该账号禁止登录");
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		
		//判断验证码对不对
		if(smsLoginVerify()) {
			EasyResult smsResult = smsCodeCheck(username);
			if(!smsResult.isOk()) {
				return smsResult;
			}
		}
		record.setPrimaryValues(RandomKit.uuid());
		record.set("username", username);
		record.set("ip", ip);
		record.set("login_time", EasyDate.getCurrentDateString());
		try{
			Integer errorLoginCount = loginErrorInfo.get(username);
			if(errorLoginCount!=null&&errorLoginCount>=10) {
				Long beiginLoginTime = loginTimeInfo.get(username);
				if(beiginLoginTime==null) {
					beiginLoginTime = System.currentTimeMillis();
					loginTimeInfo.put(username, beiginLoginTime);
				}
				//10分钟后再试试
				if(System.currentTimeMillis()-beiginLoginTime<1000*60*5) {
					return EasyResult.fail("错误次数过多请5分钟后再试！"); 
				}else {
					loginErrorInfo.remove(username);
					loginTimeInfo.remove(username);
				}
			}
			password = AesUtils.getInstance().decrypt(password);
			if(login(username, password)){
				loginErrorInfo.remove(username);
				loginTimeInfo.remove(username);
				
				String notAllocMultipleLogin = appContext.getProperty("notAllocMultipleLogin", "0");
				if("1".equals(notAllocMultipleLogin)&&LoginSessionUtils.isLoginState(username)) {
					return EasyResult.fail("账号已在其他地方登录。");
				}
				
				try {
					this.getConsoleQuery().executeUpdate("update EASI_USER set LAST_LOGIN_IP = ?,LAST_LOGIN_TIME = ? where LOGIN_ACCT = ?", WebKit.getIP(getRequest()),EasyDate.getCurrentDateString(),username);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
				
				record.set("msg", "登录成功");
				result.setMsg("登录成功");
				result.setUrl(Constants.getContextPath()+"/index");
				
				// 检查密码修改时间
				int limitDay = Integer.valueOf(appContext.getProperty("changePwdlimitDay", "0"));
				try {
					if(limitDay>0) {
						EasyRow  row = null;
						if("0".equals(loginAccountSource)) {
							row = this.getConsoleQuery().queryForRow("select LAST_PWD_CHANGE_TIME from EASI_USER where LOGIN_ACCT = ?", new Object[] {username});
						}else {
							//ALTER TABLE ycmain.easi_user_login ADD COLUMN `LAST_PWD_CHANGE_TIME` varchar(19) NULL DEFAULT '' COMMENT '最后修改密码时间' AFTER `LAST_LOGIN_TIME`;
							row = this.getSysDefaultQuery().queryForRow("select LAST_PWD_CHANGE_TIME from EASI_USER_LOGIN where USER_ACCT = ?", new Object[] {username});
						}
						String lastPwdChangeTime = row.getColumnValue("LAST_PWD_CHANGE_TIME");
						if(!StringUtils.isBlank(lastPwdChangeTime)) {
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							long lastChangeTime = sdf.parse(lastPwdChangeTime).getTime();
							long currentTime = System.currentTimeMillis();
							long diffDays = (currentTime - lastChangeTime) / (1000 * 60 * 60 * 24);
							if(diffDays >= limitDay) {
								result.setUrl(Constants.getContextPath()+"/servlet/user/editPwd");
							}
						}
					}
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
				
				HttpSession session = this.getRequest().getSession();
				session.setMaxInactiveInterval(60*30);
				session.setAttribute("MARS_CONSOLE_USER", username);
				session.setAttribute("MARS_CONSOLE_ROLE", getRoleType(username));
				LoginSessionUtils.addLoginInfo(session.getId(), username);
			}else{
				if(loginErrorInfo.get(username)==null) {
					loginErrorInfo.put(username, 1);
					loginTimeInfo.put(username, System.currentTimeMillis());
				}else{
					loginErrorInfo.put(username, errorLoginCount+1);
				}
				this.error("password is error", null);
				record.set("msg", "登录失败");
				result.addFail("账号或密码错误！");	
			}
		}catch(Exception ex){
			this.error(ex.getMessage(), ex);
			result.addFail("账号或密码错误！");
		}
		try {
			this.getConsoleQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
//		if(result.isOk()&&"0".equals(loginAccountSource)&&"admin@mars".equals(username)) {
//			String editResult = resetUserInfo(username);
//			if(editResult!=null) {
//				result.setState(2);
//				result.setMsg(editResult);
//			}
//		}
		return result;
	}

	
	@Override
	protected String getResId() {
		return null;
	}
	
	private boolean login(String username,String password){
		String loginAccountSource = appContext.getProperty("loginAccountSource", "0");
		if("0".equals(loginAccountSource)) {
			return consoleLogin(username,password);
		}else {
			try {
				this.info("login type is from easi_user_login",null);
				String dbpwd = this.getSysDefaultQuery().queryForString("select USER_PWD from EASI_USER_LOGIN where USER_ACCT = ? and ACCT_STATE = 0", username);
				if(StringUtils.isBlank(dbpwd)) {
					this.error(username+">in easi_user_login did not Find The Account.", null);
					return false;
				}
				password = MD5Util.getHexMD5(password);
				return password.equalsIgnoreCase(dbpwd);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return consoleLogin(username,password);
			}
		}
	}
	
	private boolean consoleLogin(String username,String password) {
		password = password.trim();
		if(StringUtils.isBlank(password)) {
			this.error("consoleLogin password is empty", null);
		}
		String sql="select LOGIN_PWD from EASI_USER WHERE LOGIN_ACCT = ?";
		String dbpwd  = null;
		try {
			dbpwd = this.getConsoleQuery().queryForString(sql, new Object[]{username});
			if(StringUtils.isBlank(dbpwd)) {
				this.error("用户["+username+"]登录Console失败，原因：账号不存在！",null);
				return false;
			}
			 if(dbpwd.startsWith("AES")){
				dbpwd = dbpwd.replace("AES_", "");
				dbpwd = AesUtils.decrypt(dbpwd, AesUtils.USER_KEY);
			}else {
				this.error("用户["+username+"]密码需要加密更新！",null);
				return false;
			}
		} catch (SQLException e) {
			this.error("用户["+username+"]登录Console失败！",e);
		}
//		this.warn("consoleLogin>"+password+","+dbpwd, null);
		return password.equalsIgnoreCase(dbpwd);
	}
	
	/**
	 * 1：管理员 2:只读权限  4：超级管理员 9：日志查看
	 * @param username
	 * @return
	 */
	private int getRoleType(String username){
		if(isConsoleAccount()) {
			String sql="select ROLE_ID from EASI_USER WHERE LOGIN_ACCT = ?";
			try {
				String roleType = this.getConsoleQuery().queryForString(sql, new Object[]{username});
				return Integer.valueOf(roleType);
			} catch (SQLException e) {
				this.error(null, e);
				return 1;
			}
		}
		return 1;
	}
	
	public void actionForReload(){
		ServerContext.reload();
		LoginSessionUtils.removeAll();
		renderText("重载成功!");
	}
	
	
	public void actionForClearConf(){
		try {
			int result  = 0;
			String action = getPara("operate");
			if("KeySwith".equals(action)) {
				result = getConsoleQuery().executeUpdate("update EASI_CONF set CONF_VALUE = '0'  where CONF_KEY = ?",new Object[]{"SECURITY_ENTER"});
			}
			else if("hostBlank".equals(action)) {
				result = getConsoleQuery().executeUpdate("update EASI_CONF set CONF_VALUE = ''  where CONF_KEY = ?",new Object[]{"hostBlankList"});
			}
			else if("securityEnter".equals(action)) {
				result = getConsoleQuery().executeUpdate("update EASI_CONF set CONF_VALUE = '0'  where CONF_KEY = ?",new Object[]{"SECURITY_ENTER"});
			}
			else if("allocAccessIp".equals(action)) {
				result = getConsoleQuery().executeUpdate("update EASI_CONF set CONF_VALUE = ''  where CONF_KEY = ?",new Object[]{"CONSOLE_ALLOC_ACCESS_IP"});
			}
			else if("allocAccessServerAddr".equals(action)) {
				result = getConsoleQuery().executeUpdate("update EASI_CONF set CONF_VALUE = ''  where CONF_KEY = ?",new Object[]{"ALLOC_ACCESS_SERVER_ADDR"});
			}
			else if("loginAuthCode".equals(action)) {
				result = getConsoleQuery().executeUpdate("update EASI_CONF set CONF_VALUE = 'false'  where CONF_KEY = ?",new Object[]{"G_LOGIN_AUTH"});
			}
			this.info("operate:"+action+">result:"+result, null);
		} catch (SQLException e) {
			renderJson(e.getMessage());
		}
	}
	
	public EasyResult smsVerifyCheck() {
		JSONObject data = this.getJSONObject();
		EasyResult loginAuth = loginAuth(data.getString("j_imagecode"),true);
		if(!loginAuth.isOk()) {
			return loginAuth;
		}
		String  username = StringFilter.HTMLEncode(StringUtils.trimToEmpty(data.getString("j_username"))); 
		try {
			JSONObject result = new JSONObject();
			result.put("username",username);
			result.put("mobile","");
			result.put("userId","");
			
			EasyQuery query = this.getConsoleQuery();
			String sql="select LOGIN_ACCT USER_ACCT,USER_ID,MOBILE from EASI_USER where LOGIN_ACCT = ? or MOBILE = ?";
			if(!isConsoleAccount()) {
				query = this.getQuery();
				sql="select t2.USER_ACCT,t1.USER_ID,t1.MOBILE from EASI_USER t1,easi_user_login t2 where t1.USER_ID = t2.USER_ID and t2.USER_ACCT = ? or t1.MOBILE = ?";
			}
			JSONObject object = query.queryForRow(sql, new Object[]{username,username},new JSONMapperImpl());
			if(object!=null&&!object.isEmpty()) {
				String userAcct =  object.getString("USER_ACCT");
				String mobile =  object.getString("MOBILE");
				String userId =  object.getString("USER_ID");
				if(StringUtils.isBlank(mobile)) {
					this.warn(username+","+userAcct+"没绑定号码，不允许登录", null);
					return EasyResult.fail(userAcct+"没有绑定手机号码");
				}
				result.put("mobile", mobile);
				result.put("userId", userId);
				return EasyResult.ok(result);
			}else {
				return EasyResult.fail("账号不存在");
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	//获取短信验证码
	public EasyResult actionForGetSmsCode(){
		if(!this.getMothed().equals("post")) { 
			return EasyResult.fail("非法访问");
		}
		JSONObject data = getJSONObject();
		String imagecode = data.getString("j_imagecode");
		EasyResult loginAuth = loginAuth(imagecode);
		if(!loginAuth.isOk()) {
			return loginAuth;
		}
		//短信失效时间，单位分钟
		int expiration = Integer.parseInt(StringUtils.trim(appContext.getProperty("SEND_SMS_EXPIRATION", "10")));
		String smsCode = RandomKit.smsAuthCode(6, SMSAuthCodeType.Numbers);
		String username = data.getString("j_username");
		if(StringUtils.isBlank(username)) {
			return EasyResult.fail("账号不能为空.");
		}
		EasyResult result = smsVerifyCheck();
		if(!result.isOk()) {
			return result;
		}
		JSONObject mobileInfo = result.getJSONObject("data");
		String mobile = mobileInfo.getString("mobile");
		String userId  = mobileInfo.getString("userId");
		try {
			try {
				boolean sendSmsVerify = sendSmsVerify(userId);
				if(!sendSmsVerify){
					this.warn(userId+","+username+"获取验证码太频繁,请稍后再试!",null);
					return EasyResult.fail("获取验证码太频繁,请稍后再试!");
				}
				JSONObject params = new JSONObject();
				params.put("mobile", mobile);
				params.put("smsId", RandomKit.uniqueStr(1, false));//下发唯一标识,长度不超过20,可为空，可用于匹配状态报告
				params.put("subcode", "");//签名扩展码，在短信平台申请的签名对应的扩展码
				params.put("smsCode", smsCode);
				params.put("userId", userId);
				params.put("expiration", expiration);
				String message = "您正在登录验证,验证码"+smsCode+",切勿将验证码泄露于他人,本条验证码有效期"+expiration+"分钟。";
				params.put("content", message);
				
				IService service = ServiceContext.getService("YC-SMS-SEND-SMS-SERVICE");
				if(service==null) {
					this.error("YC-SMS-SEND-SMS-SERVICE not exsit.", null);
					return EasyResult.fail("短信接口不存在.");
				}else {
					JSONObject responeJson = service.invoke(params);
					this.info("sms:"+responeJson,null);
				}
			} catch (Exception e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail("获取验证码太频繁,请稍后再试!");
			}
			long ts = System.currentTimeMillis();
			this.info(mobile+"短信验证码:"+smsCode,null);
			
			int resultRow = 1;
			if(isConsoleAccount()) {
				String sql="update EASI_USER set AUTH_CODE= ?,AUTH_CODE_TIME = ?  where USER_ID = ?";
				this.getConsoleQuery().executeUpdate(sql,smsCode,ts,userId);
			}else {
				String sql="update EASI_USER set AUTH_CODE= ?,AUTH_CODE_TIME = ?  where USER_ID = ?";
				this.getQuery().executeUpdate(sql,smsCode,ts,userId);
			}
			
			if(resultRow>0){
				return EasyResult.ok(ts, "验证码发送成功,"+expiration+"分钟内有效！");
			}else{
				return EasyResult.ok(ts, "验证码("+smsCode+")发送成功至"+mobile+";"+expiration+"分钟内有效！");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(),e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult smsCodeCheck(String username) {
			JSONObject jsonObject = getJSONObject();
			String code = jsonObject.getString("smsCode");
			JSONObject resultJson = null;
			try {
				if(isConsoleAccount()) {
					String sql="select LOGIN_ACCT USER_ACCT,USER_ID,MOBILE,AUTH_CODE,AUTH_CODE_TIME from EASI_USER where LOGIN_ACCT = ? or MOBILE = ?";
					resultJson = this.getConsoleQuery().queryForRow(sql, new Object[]{username,username}, new JSONMapperImpl());
				}else {
					String sql="select t2.USER_ACCT,t1.USER_ID,t1.MOBILE,t1.AUTH_CODE,t1.AUTH_CODE_TIME from EASI_USER t1,EASI_USER_LOGIN t2 where t1.USER_ID = t2.USER_ID and t2.USER_ACCT = ? or t1.MOBILE = ?";
					resultJson = this.getQuery().queryForRow(sql, new Object[]{username,username}, new JSONMapperImpl());
				}
			}catch (Exception e) {
				this.error(e.getMessage(), e);
				try {
					ScriptHelper.getService().checkField();
				} catch (SQLException ex) {
					this.error(ex.getMessage(), ex);
				}
			}
		try {
			String smsLoginMustHasMobile = appContext.getProperty("smsLoginMustHasMobile", "0");
			if(resultJson==null){
				if("1".equals(smsLoginMustHasMobile)) {
					this.warn(username+"账号不存在!",null);
					return EasyResult.fail("账号或密码错误!");
				}else {
					return EasyResult.ok();
				}
			}
			String mobile = resultJson.getString("MOBILE");
			if("0".equals(smsLoginMustHasMobile)&&StringUtils.isBlank(mobile)) {
				this.warn(username+"账号>mobile is empty,but>smsLoginMustHasMobile is 0!",null);
				return EasyResult.ok();
			}
			
			String userId = resultJson.getString("USER_ID");
			String authenCode =  resultJson.getString("AUTH_CODE");
			String authenCodeTime =  resultJson.getString("AUTH_CODE_TIME");
			if(StringUtils.isBlank(code)) {
				return EasyResult.fail("短信验证码不能为空");
			}
			if(StringUtils.isBlank(authenCode)) {
				return EasyResult.fail("请先获取短信验证码");
			}
			Long  timestrap = Long.valueOf(authenCodeTime);
			long currentMills=System.currentTimeMillis();
			  if(currentMills-timestrap>(1000*60*10)){
				  return EasyResult.fail("短信验证码已经超时,请重新获取");
			  }
			if(code.equals(authenCode)) {
				if(isConsoleAccount()) {
					this.getConsoleQuery().executeUpdate("update easi_user set AUTH_CODE = '' where USER_ID = ?",userId );
				}else {
					this.getQuery().executeUpdate("update easi_user set AUTH_CODE = '' where USER_ID = ?",userId );
				}
				return EasyResult.ok();
			}else {
				return EasyResult.fail("短信验证码不正确");
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
		
	/**
	 * 是否符合发送短信验证码条件
	 * @param userId
	 * @return true标识可以继续发
	 */
	private boolean sendSmsVerify(String userId) {
		String sql = "select auth_code_time from easi_user where user_id = ?";
		try {
			String time = null;
			if(isConsoleAccount()) {
				time = this.getConsoleQuery().queryForString(sql, userId);
			}else {
				time = this.getQuery().queryForString(sql, userId);
			}
			if(StringUtils.isNotBlank(time)) {
				Long sendTime = Long.valueOf(time);
				long currentTime=System.currentTimeMillis();
				if(currentTime-sendTime<=60000){
					return false;
				}
			}
			return true;
		} catch (SQLException e) {
			this.error(null, e);
			return true;
		}
	}
	

	

	private EasyResult loginAuth(String imagecode) {
		return loginAuth(imagecode,false);
	}
	
	private EasyResult loginAuth(String imagecode,boolean must) {
		String loginAuth=ServerContext.getProperties("G_LOGIN_AUTH","true");
		if("true".equalsIgnoreCase(loginAuth)||must){
			//判断验证码不能为空
			String  authenCode = StringFilter.HTMLEncode(StringUtils.trimToEmpty(imagecode));
			if(StringUtils.isBlank(authenCode)){  //判断验证码没有输入的情况。
				return EasyResult.fail("请输入图形验证码！");
			}
			//判断输入认证码和session的认证码是否一致
			String randyString = (String)this.getRequest().getSession().getAttribute("randyString");
			if(StringUtils.isBlank(randyString)){  //如果验证码已经过期的时候
				return EasyResult.fail("图形验证码已失效！");
			}
			if(!authenCode.equalsIgnoreCase(randyString)){
				return EasyResult.fail("图形验证码输入错误!");
			}
		}
		return EasyResult.ok();
	}
	
	
	private String resetUserInfo(String username) {
		try {
			String account = AccountGenerator.generateAccount();
			String pwd = AccountGenerator.generateSecurePassword();
			this.info("pwd>"+pwd,null);
			String _pwd = "AES_"+AesUtils.encrypt(pwd, AesUtils.USER_KEY);
			int result = this.getConsoleQuery().executeUpdate("update EASI_USER set LOGIN_ACCT = ?,LOGIN_PWD = ? where LOGIN_ACCT = ?",account,_pwd,username);
			if(result>0) {
				addOperateLog("首次登录重置账号："+username+">"+account, "");
				EasyResult securityKeyResult = ScriptHelper.getService().addSecurityKey();
				String key = securityKeyResult.getData().toString();
				String newUrl = getRequest().getRequestURL().toString()+"?key="+key;
				this.info("重置账号密码username："+username+">"+account+">"+_pwd+">url:"+newUrl, null);
				String msg= "'<div class=\"reser-user-msg\">账号 "+username+" 已重置，新账号："+account+"，密码："+pwd+" 请妥善保存并使用新信息登录。</div><br><br><div class=\"reser-user-msg-a\">新登录链接：<a href=\""+newUrl+"\">"+newUrl+"</a></div>";
		
				String basePath = Globals.SERVER_DIR;
	    		File confFile = new File(basePath+File.separator+"install.txt");
	    		if(confFile.exists()) {
	    			confFile.delete();
	    		}
    			try {
					confFile.createNewFile();
					String text  = "安装时间："+EasyDate.getCurrentDateString()+"\n\n Console初始化账号："+account+" \n\n 访问方式："+newUrl+" \n\n 请妥善保存当前文件中的关键信息，使用后请及时清空文件内容。";
					FileKit.saveToFile(text, confFile.getAbsolutePath(),"utf-8");
    			} catch (IOException e) {
    				this.error(e.getMessage(), e);
				}
				return msg;
			}else {
				this.error(username+"重置账号密码失败", null);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return null;	
	}
	
    
	
	
	/**
	 * @return true表示需要短信校验
	 */
	public boolean smsLoginVerify() {
		String smsLoginVerify = appContext.getProperty("smsLoginVerify", "0");
		return "1".equals(smsLoginVerify);
	}
	
	public boolean isConsoleAccount() {
		String loginAccountSource = appContext.getProperty("loginAccountSource", "0");
		return "0".equals(loginAccountSource);
	}
	
	public void loginForImageCode(){
		this.getResponse().setContentType("image/jpeg");
		try {
			OutputStream outstream=this.getResponse().getOutputStream();
			VerifyCodeKit vCode = new VerifyCodeKit(80, 30, 6, 20);
			String code = vCode.getRandomString();
			vCode.write(getResponse().getOutputStream());
			this.getRequest().getSession(true).setAttribute("randyString",code);
			outstream.flush();
			outstream.close();
		} catch (IOException e) {
			e.printStackTrace();
			this.error(e.getMessage(), e);
		}
	}
}
