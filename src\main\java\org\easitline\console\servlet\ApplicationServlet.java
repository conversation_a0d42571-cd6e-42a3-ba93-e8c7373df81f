package org.easitline.console.servlet;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.commons.io.FileUtils;
import org.easitline.common.core.EasyPool;
import org.easitline.common.core.EasyServer;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.base.Constants;
import org.easitline.console.deploy.vo.AppConfig;
import org.easitline.console.service.ApplicationService;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.service.ResUpdateService;
import org.easitline.console.utils.AesUtils;
import org.easitline.console.utils.SafeRequestWrapper;
import org.easitline.console.utils.ScriptHelper;
import org.easitline.console.vo.AppConfigModel;
import org.easitline.console.vo.AppConfigRowMapper;
import org.easitline.console.vo.AppdsModel;
import org.easitline.console.vo.AppdsRowMapper;
import org.easitline.console.vo.ApplicationHisRowMapper;
import org.easitline.console.vo.ApplicationModel;
import org.easitline.console.vo.ApplicationRowMapper;
import org.easitline.console.vo.DatasourceModel;
import org.easitline.console.vo.DatasourceRowMapper;
import org.easitline.console.vo.DictModel;
import org.easitline.console.vo.ResourceModel;

import com.alibaba.fastjson.JSONObject;


/**
 * 系统数据源管理类
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/application/*")
@MultipartConfig(maxFileSize=1024 * 1024 * 150)
public class ApplicationServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private static final String appsBasePath = Globals.BASE_DIR+File.separator+"easyserver"+File.separator+"apps";
	
	private String deployDir() {
		return ApplicationService.deployDir();
	}
	
	private Map<String,String> getAppConfigs(String appId){
		return ApplicationService.getAppConfigs(appId);
	}
	
	public EasyResult actionForHisList() throws SQLException {
		JSONObject jsonObject = this.getJSONObject();
		String appId=jsonObject.getString("appId");
		String sql = "select * from EASI_APP_INFO_DEPLOY_HIS where APP_ID = ?";
		List<Map<String, String>> list = this.getConsoleQuery().queryForList(sql, new Object[]{appId},new MapRowMapperImpl());
		return EasyResult.ok(list);
	}
	public EasyResult actionForGetAppConfig(){
		JSONObject jsonObject = this.getJSONObject();
		String appId=jsonObject.getString("appId");
		return EasyResult.ok(AppContext.getContext(appId).getPropertys());
	}
	
	private ApplicationModel setAppInfo(String appId){
		String sql = "select * from EASI_APP_INFO where APP_ID = ?";
		ApplicationModel app;
		try {
			app = this.getConsoleQuery().queryForRow(sql, null,new ApplicationRowMapper());
			this.getRequest().setAttribute("app", app);
			return app;
		} catch (Exception ex) {
			this.error("获得应用["+appId+"]信息失败，原因："+ex.getMessage(), ex);
		}
		return null;
		
	}
	
	public void actionForDownloadWar() {
		String warName = getPara("warName");
		String path = deployDir()+File.separator+warName;
		File file = new File(path);
		if(!file.exists()) {
			renderHtml(path+">not exist.");
			return;
		}
		renderFile(file, warName,false);
	}
	
	public EasyResult actionForReloadAppWar() {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		String path = Globals.SERVER_DIR+File.separator +"war";
		File file = new File(path);
		if(!file.exists()){
			file.mkdirs();
		}
		File[] files  = file.listFiles();
		for(File warFile:files) {
			String fileName  = warFile.getName();
			if(fileName.lastIndexOf(".war")>-1) {
				ApplicationModel appInfo = this.getAppInfo(warFile, "META-INF/appinfo.xml");
				EasyRecord record = new EasyRecord("EASI_APP_STROE","APP_ID");
				try {
					record.set("APP_ID", appInfo.getAppId());
					record.set("APP_NAME", appInfo.getAppName());
					record.set("APP_VERSION", appInfo.getAppVersion());
					record.set("APP_VERSION_DESC", appInfo.getAppVersionDesc());
					record.set("WAR_NAME",appInfo.getWarName());
					record.set("WAR_SIZE",appInfo.getWarSize());
					record.set("DEPLOY_TIME", appInfo.getDeployTime());
					record.set("LAST_MODIFIED", appInfo.getLastModified());
					boolean bl  =  this.getConsoleQuery().update(record);
					if(!bl) {
						this.getConsoleQuery().save(record);
					}
				} catch (SQLException e) {
					this.error(null, e);
				}
				
			}
		}
		return EasyResult.ok();
	}
	
	/**
	 * 添加保存
	 * @param req
	 * @param resp
	 * @throws Exception 
	 */
	public EasyResult actionForDeploy() throws Exception {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		try {
			JSONObject jsonObject = this.getJSONObject();
			File tmpWarFile = new java.io.File(FileKit.getTempFilePath("apps") + File.separator + jsonObject.getString("appWarName"));
			if(!tmpWarFile.exists()){
				return EasyResult.fail("请重上传war!");
			}
			
			//更新或生成应用信息
			ApplicationModel appInfo = this.getAppInfo(tmpWarFile, "META-INF/appinfo.xml");
			String appVersionDesc=jsonObject.getString("appVersionDesc");
			appInfo.setAppVersionDesc(appVersionDesc);
			
			if(appInfo.getWarName().indexOf(".jar")>-1) {
				appInfo.setFileType("jar");
				String depoyDir=jsonObject.getString("depoyDir");
				if(StringUtils.isBlank(depoyDir)) {
					return EasyResult.fail("请填写部署目录");
				}
				File fs = new File(depoyDir);
				if(!fs.exists()) {
					fs.mkdirs();
				}
				appInfo.setDepoyDir(depoyDir);
			}else {
				appInfo.setFileType("war");
				appInfo.setDepoyDir(deployDir());
			}
			
			this.addOperateLog("部署应用"+appInfo.getWarName(),JSONObject.toJSONString(appInfo));

			String destPath = appInfo.getDepoyDir() + File.separator + jsonObject.getString("appWarName");
			appInfo.setAppDescInfo(jsonObject); //保存应用和数据源对应关系信息
			appInfo.setAppFilePath(destPath);
			this.debug("文件目录："+tmpWarFile, null);
			this.saveAppInfo(appInfo);
			
			//更新或生成配置信息
			List<AppConfig> appConfies = this.getAppConfig(appInfo.getAppId(), appInfo.getAppVersion(), tmpWarFile, "META-INF/appconfig.xml"); // 读出文件内容
			this.debug("应用配置信息："+JSONObject.toJSONString(appConfies), null);
			this.updateAppConfig(appInfo,appConfies);
		
			//更新或生成资源信息
			List<ResourceModel> appResources = this.getResourceInfo(appInfo.getAppId(), appInfo.getAppVersion(), tmpWarFile,"META-INF/appresource.xml");
			this.debug("应用资源信息："+JSONObject.toJSONString(appResources), null);
			this.updateAppResource(appInfo, appResources);
			this.debug("是否执行库表初始化操作："+jsonObject.getString("dbinit"), null);
			
			//生成应用数据字典
			List<DictModel> appDicts = this.getDictInfo(appInfo.getAppId(), appInfo.getAppVersion(), tmpWarFile,"META-INF/appdict.xml");
			this.debug("更新应用数据字典信息,字典表数量："+appDicts.size(), null);
			ApplicationService.updateAppDict(appDicts);
			this.debug("是否执行库表初始化操作："+jsonObject.getString("dbinit"), null);
			
			File tmpDir = new File(appsBasePath+ File.separator +appInfo.getAppId());
		    if (!tmpDir.isDirectory()||!tmpDir.exists())	tmpDir.mkdir();

			AppContext.getContext(appInfo.getAppId(), true);//重新加载应用
			//创建应用所需要的库表
			if(!StringUtils.isBlank(jsonObject.getString("dbinit"))){
				initAppDbscript(appInfo.getAppId(),tmpWarFile);  
			}
			
			String deployedFileName = appInfo.getWarName().replace(".war",".deployed");
			File deployedFile = new java.io.File(deployDir() + File.separator + deployedFileName);
			if(deployedFile.exists()) {
				deployedFile.delete();
				Thread.sleep(2000);
			} 
			
			File destFile = new java.io.File(destPath);
			if(destFile.isFile())  destFile.delete(); 
			this.debug("开始更新WAR应用目标文件："+destFile, null);
			FileUtils.copyFile(tmpWarFile, destFile);  
			this.addHis(destFile,appInfo);

			if(tmpWarFile.exists())tmpWarFile.delete();
			
			return  EasyResult.ok(null,"部署成功，请从应用监控列表中进行查看应用状态！");
		} catch (Exception ex) {
			this.error("部署应用失败，原因："+ex.getMessage(), ex);
			return EasyResult.error(501, "部署应用失败，原因："+ex.getMessage());
		}
	}
	
	
	public void actionForUpdateAllDs()throws Exception{
		if(!checkOperateTime().isOk()) {
			renderJson(checkOperateTime());
			return;
		}
		this.getConsoleQuery().executeUpdate("update EASI_APP_DS set DS_NAME  = ?",getPara("dsName"));
		renderText("重载成功！");
	}
	
	public EasyResult actionForReloadAll(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		List<JSONObject> list=null;
		try {
			this.addOperateLog("重载所有应用","");
		    list=this.getConsoleQuery().queryForList("select * from EASI_APP_INFO",null,new JSONMapperImpl());
			if(list!=null){
				for(JSONObject jsonObject:list){
					String appId=jsonObject.getString("APP_ID");
					AppContext.getContext(appId, true);//重新加载应用
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(list,"重载成功!");
	}
	
	public EasyResult actionForSwitchVersion() throws SQLException, IOException{
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		
		String id=getJsonPara("id");
		ApplicationModel appInfo = this.getConsoleQuery().queryForRow("select * from EASI_APP_INFO_DEPLOY_HIS where ID = ?", new Object[]{id},new ApplicationHisRowMapper());
		String path=appInfo.getAppFilePath();
		
		File fromFile = new java.io.File(path);
		if(fromFile==null|| !fromFile.exists()){
			return EasyResult.ok(null,"版本文件不存在，切换失败!");
		}
		
		this.addOperateLog("切换版本"+appInfo.getWarName()+"#"+appInfo.getAppVersion(),"");
		
		EasyRecord record=new EasyRecord("EASI_APP_INFO", "APP_ID");
		record.setPrimaryValues(appInfo.getAppId());
		record.set("DEPLOY_TIME",EasyDate.getCurrentDateString());
		record.set("APP_VERSION", appInfo.getAppVersion());
		record.set("WAR_NAME",appInfo.getWarName());
		record.set("APP_NAME", appInfo.getAppName());
		record.set("APP_VERSION_DESC", appInfo.getAppVersionDesc());
		record.set("LAST_MODIFIED", appInfo.getLastModified());
		record.set("WAR_SIZE", appInfo.getWarSize());
		this.getConsoleQuery().update(record);
		
		
		File destFile = new java.io.File(deployDir() + File.separator + appInfo.getWarName());
		if(destFile.isFile())  destFile.delete(); 
		try {
			Thread.sleep(2000);
		} catch (InterruptedException e) {
			this.error(e.getMessage(), e);
		}
		FileUtils.copyFile(fromFile, destFile);  
		
		return EasyResult.ok(null,"切换成功!");
	}
	
	public EasyResult actionForUploadSqlFile()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		try {
			Part part=this.getFile("appFile");
			String filename =part.getSubmittedFileName();
			String separator = File.separator;
			if(StringUtils.isNotBlank(filename)&&filename.startsWith(separator)){
				filename = filename.substring(filename.lastIndexOf(separator),filename.length());
			}
			if(filename.indexOf(".sql")==-1&&filename.indexOf(".json")==-1) {
				part.delete();
				return EasyResult.fail("文件格式不合法");
			}
			
			String path=Globals.DB_DIR+File.separator+"schema";
			if (!new File(path).exists()) {
				new File(path).mkdirs();
			}
			
			File tmpWarFile = new java.io.File(path + separator + filename);
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			
			FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
			part.delete();
			
			result.put("state", 1);
			result.put("msg", "上传成功!");
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}
	}
	
	public EasyResult actionForRunUpgrade()  throws SQLException, IOException, ServletException {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		EasyResult result = new EasyResult();
		JSONObject params = getJSONObject();
		String appId = SafeRequestWrapper.getParameter(getRequest(), "appId");
		String path = params.getString("path");
		try {
			Part part = null;
			ApplicationModel appInfo = null;
			boolean localFile = StringUtils.isBlank(path);
			String separator = File.separator;
			File tmpWarFile = null;
			
			if(localFile) {
				part=this.getFile("appFile");
				String filename =part.getSubmittedFileName();
				//防止读取文件的时候读取到全路径
				if(StringUtils.isNotBlank(filename)&&filename.startsWith(separator)){
					filename = filename.substring(filename.lastIndexOf(separator),filename.length());
				}
				if(filename.indexOf(" ")>-1){
					part.delete();
					return EasyResult.fail("上传失败,war文件名有空格.");
				}
				
				tmpWarFile = new java.io.File(FileKit.getTempFilePath("apps") + separator + filename);
				if (tmpWarFile.exists()) {
					tmpWarFile.delete();
				}
				
				if(filename.indexOf(".war")==-1&&filename.indexOf(".jar")==-1){
					tmpWarFile.delete();
					part.delete();
					result.put("state", 0);
					result.put("msg", "请上传war或jar文件");
					return result;
				}
				FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
				part.delete();
				
				appInfo = this.getAppInfo(tmpWarFile, "META-INF/appinfo.xml");
				if(!appInfo.getAppId().equals(appId)){
					if(tmpWarFile.exists())tmpWarFile.delete();
					result.put("state", 0);
					result.put("msg", "升级失败:您上次的war的appid["+appInfo.getAppId()+"]不是"+appId);
					tmpWarFile.delete();
					part.delete();
					return result;
				}
				if(filename.indexOf(".war")>-1) {
					appInfo.setFileType("war");
				}else {
					appInfo.setFileType("jar");
				}
			}else {
				this.info("指定文件war路径升级:"+path,null);
				File warFile = new File(path);
				tmpWarFile = new java.io.File(FileKit.getTempFilePath("apps") + separator + warFile.getName());
				if (tmpWarFile.exists()) {
					tmpWarFile.delete();
				}
				FileKit.copyFile(path, tmpWarFile.getAbsolutePath());
				appInfo = this.getAppInfo(tmpWarFile, "META-INF/appinfo.xml");
			}
			
			this.addOperateLog("升级应用"+appInfo.getWarName()+"#"+appInfo.getAppVersion(),JSONObject.toJSONString(appInfo));
			
			//更新或生成配置信息
			List<AppConfig> appConfies = this.getAppConfig(appInfo.getAppId(), appInfo.getAppVersion(), tmpWarFile,	"META-INF/appconfig.xml"); // 读出文件内容
			this.debug("应用配置信息："+JSONObject.toJSONString(appConfies), null);
			this.updateAppConfig(appInfo,appConfies);


			//更新或生成资源信息
			List<ResourceModel> appResources = this.getResourceInfo(appInfo.getAppId(), appInfo.getAppVersion(), tmpWarFile,"META-INF/appresource.xml");
			this.debug("应用资源信息："+JSONObject.toJSONString(appResources), null);
			this.updateAppResource(appInfo, appResources);
			
			//生成应用数据字典
			List<DictModel> appDicts = this.getDictInfo(appInfo.getAppId(), appInfo.getAppVersion(), tmpWarFile,"META-INF/appdict.xml");
			this.debug("更新应用数据字典信息,字典表数量："+appDicts.size(), null);
			ApplicationService.updateAppDict(appDicts);
			
			
			
			String basePath = deployDir();
			if(appInfo.getWarName().indexOf(".jar")>-1){
				basePath = this.getConsoleQuery().queryForString("select DEPOY_DIR from EASI_APP_INFO where app_id = ?", appInfo.getAppId());
			}
			
			if(Constants.EASITLINE_CONSOLE.equals(appId)) {
				String consoleDeployDir = ServerContext.getProperties("CONSOLE_DEPLOY_DIR", "");
				if(StringUtils.isNotBlank(consoleDeployDir)) {
					if(!consoleDeployDir.startsWith("/")) {
						basePath = Globals.BASE_DIR+File.separator+consoleDeployDir;
					}else {
						basePath = consoleDeployDir;
					}
				}
			}
			
			File destFile = new java.io.File(basePath + separator + appInfo.getWarName());
			if(destFile.isFile())  destFile.delete();
			
			String deployedFileName = appInfo.getWarName().replace(".war",".deployed");
			File deployedFile = new java.io.File(basePath + separator + deployedFileName);
			if(deployedFile.exists()) {
				deployedFile.delete();
				Thread.sleep(1000);
			}else {
				try {
					Thread.sleep(2000);
				} catch (InterruptedException e) {
					this.error(e.getMessage(), e);
				}
			} 
			
			FileUtils.copyFile(tmpWarFile, destFile);  
			
			
			this.addHis(tmpWarFile, appInfo);
			
			ApplicationService.updateAppInfo(appInfo, destFile);
			
			if(tmpWarFile.exists())tmpWarFile.delete();
			
			result.put("msg", "升级成功!");
			result.put("state", 1);
			result.put("serverInfo", EasyServer.getVersion()+"_"+EasyServer.getVersionDate()+"_"+EasyServer.getRunime());
			result.put("deployFilePath", destFile.getAbsoluteFile());
			result.put("appInfo", appInfo);
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}finally{
			AppContext.getContext(appId, true);
		}
	}
	
	@Deprecated
	public EasyResult actionForUploadCode()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		JSONObject params = getJSONObject();
		String appId = getPara("appId");
		String path = params.getString("path");
		String targetFileName = null;
		try {
			Part part = null;
			boolean localFile = StringUtils.isBlank(path);
			String separator = File.separator;
			File tmpCodeFile = null;
			
			if(localFile) {
				part = this.getFile("appFile");
				String filename = part.getSubmittedFileName();
				//防止读取文件的时候读取到全路径
				if(StringUtils.isNotBlank(filename)&&filename.startsWith(separator)){
					filename = filename.substring(filename.lastIndexOf(separator),filename.length());
				}
				
				tmpCodeFile = new java.io.File(FileKit.getTempFilePath("apps") + separator + filename);
				if (tmpCodeFile.exists()) {
					tmpCodeFile.delete();
				}
				
				if(filename.indexOf(".zip")==-1&&filename.indexOf(".tar")==-1&&filename.indexOf(".rar")==-1){
					tmpCodeFile.delete();
					part.delete();
					result.put("state", 0);
					result.put("msg", "请上传zip或tar文件");
					return result;
				}
				
				FileKit.saveToFile(part.getInputStream(), tmpCodeFile.getAbsolutePath());
				part.delete();
			}else {
				File codeFile = new File(path);
				tmpCodeFile = new java.io.File(FileKit.getTempFilePath("apps") + separator + codeFile.getName());
				if (tmpCodeFile.exists()) {
					tmpCodeFile.delete();
				}
				FileKit.copyFile(path, tmpCodeFile.getAbsolutePath());
			}
			targetFileName = tmpCodeFile.getName();
			
			this.addOperateLog("上传代码"+appId,targetFileName);
			
			
			File tmpDir = new File(appsBasePath+ File.separator +appId);
		    if (!tmpDir.isDirectory()||!tmpDir.exists())	tmpDir.mkdir();
			
			File destFile = new java.io.File(appsBasePath + File.separator +appId+File.separator+EasyDate.getCurrentDateString("yyyyMMddHHmm")+"_"+ targetFileName);
			if(destFile.isFile()) {
				destFile.delete();
			} 
			
			FileUtils.copyFile(tmpCodeFile, destFile);  
			if(tmpCodeFile.exists()) {
				tmpCodeFile.delete();
			}
			
			result.put("state", 1);
			result.put("msg", "上传代码成功!");
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}
	}
	
	private void addHis(File tempfile,ApplicationModel model) throws IOException{
		ApplicationService.addHis(tempfile, model);
	}
	
	public String actionForSelectWar() throws Exception{
		EasyResult result = new EasyResult();
		try {
			File tmpDir = new File(FileKit.getTempFilePath("apps"));
			if (!tmpDir.isDirectory())	tmpDir.mkdir();
			String separator = File.separator;
			
			JSONObject params  = getJSONObject();
			String path = params.getString("path");

			File warFile = new java.io.File(path);
			if (!warFile.exists()) {
				result.put("state", 0);
				result.put("msg", "请上传war或jar文件");
				responseWriter(result.toJSONString());
				return null;
			}
			String filename = warFile.getName();
			
			File tmpWarFile = new java.io.File(tmpDir.getAbsolutePath() + separator + filename);
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			
			FileKit.copyFile(path,tmpWarFile.getAbsolutePath());
			this.debug("上传文件成功，临时文件：" + tmpWarFile.getPath(), null);
			
			ApplicationModel appInfo = this.getAppInfo(tmpWarFile, "META-INF/appinfo.xml");
			this.debug("解析应用配置文件appinfo.xml->：appId:" + appInfo.getAppId()+",appName:"+appInfo.getAppName(), null);
			if (appInfo.getAppId() == null || appInfo.getAppId().equals("")) {
				result.put("state", 0);
				result.put("msg", "部署应用失败，原因：应用配置文件[appinfo.xml]中没有找到应用ID的定义!");
				tmpWarFile.delete();
				responseWriter(result.toJSONString());
				return null;
			}
			String sql = "select * from EASI_DS_INFO where SYS_DS_NAME <> 'default-ds'";
			List<DatasourceModel> dslist = this.getConsoleQuery().queryForList(sql, null, new DatasourceRowMapper());
			result.put("appInfo", appInfo);
			result.put("dslist", dslist);
			result.put("state", 1);
			result.put("msg", "上传文件成功！");
			responseWriter(result.toJSONString());
			return null;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.put("state", 0);
			result.put("msg", "上传失败:"+e.getMessage());
			responseWriter(result.toJSONString());
			return null;
		}
	} 
	
	public String actionForUpload() throws Exception{
		EasyResult result = new EasyResult();
		try {
			File tmpDir = new File(FileKit.getTempFilePath("apps"));
			if (!tmpDir.isDirectory())	tmpDir.mkdir();
			this.debug("上传应用文件，临时目录：" + tmpDir.getPath(), null);
			Part part=this.getFile("appFile");
			String filename = part.getSubmittedFileName();
			String separator = File.separator;
			//防止读取文件的时候读取到全路径
			if(StringUtils.isNotBlank(filename)&&filename.startsWith(separator)){
				filename = filename.substring(filename.lastIndexOf(separator),filename.length());
			}
			if(filename.length()!=filename.trim().length()){
				part.delete();
				result.put("state", 0);
				result.put("msg", "上传失败,war文件名有空格");
				responseWriter(result.toJSONString());
				return null;
			}
			File   tmpWarFile = new java.io.File(tmpDir.getAbsolutePath() + separator + filename);
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			if(!filename.endsWith(".war")&&!filename.endsWith(".jar")){
				tmpWarFile.delete();
				part.delete();
				result.put("state", 0);
				result.put("msg", "请上传war或jar文件");
				responseWriter(result.toJSONString());
				return null;
			}
			
			FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
			this.debug("上传文件成功，临时文件：" + tmpWarFile.getPath(), null);
			part.delete();
			ApplicationModel appInfo = this.getAppInfo(tmpWarFile, "META-INF/appinfo.xml");
			this.debug("解析应用配置文件appinfo.xml->：appId:" + appInfo.getAppId()+",appName:"+appInfo.getAppName(), null);
			if (appInfo.getAppId() == null || appInfo.getAppId().equals("")) {
				result.put("state", 0);
				result.put("msg", "部署应用失败，原因：应用配置文件[appinfo.xml]中没有找到应用ID的定义!");
				tmpWarFile.delete();
				part.delete();
				responseWriter(result.toJSONString());
				return null;
			}
			if(filename.indexOf(".jar")>-1) {
				appInfo.setFileType("jar");
			}else {
				appInfo.setFileType("war");
			}
			
			String sql = "select * from EASI_DS_INFO where SYS_DS_NAME <> 'default-ds'";
			List<DatasourceModel> dslist = this.getConsoleQuery().queryForList(sql, null, new DatasourceRowMapper());
			result.put("appInfo", appInfo);
			result.put("dslist", dslist);
			result.put("state", 1);
			result.put("msg", "上传文件成功！");
			responseWriter(result.toJSONString());
			return null;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.put("state", 0);
			result.put("msg", "上传失败:"+e.getMessage());
			responseWriter(result.toJSONString());
			return null;
		}
	} 

	
	private void saveAppInfo(ApplicationModel appInfo) throws SQLException{
		ApplicationService.saveAppInfo(appInfo);
	}
	

	private void updateAppConfig (ApplicationModel appInfo, List<AppConfig>  appConfies ) throws SQLException{
		ApplicationService.updateAppConfig(appInfo, appConfies);
	}
	
	private List<DictModel> getDictInfo(String appId, String version, File warFile, String resourcePath) {
		return ApplicationService.getDictInfo(appId, version, warFile, resourcePath);
	}
	
	
	
	private  ApplicationModel getAppInfo(File warFile,String resourcePath){
		return ApplicationService.getAppInfo(warFile, resourcePath);
	}
	
	
	/**
	 * 获得应用配置文件信息
	 * @param appId
	 * @param version
	 * @param warFile
	 * @param resourcePath
	 * @return
	 */
	private List<AppConfig> getAppConfig(String appId,String version,File warFile,String resourcePath) {
		return ApplicationService.getAppConfig(appId, version, warFile, resourcePath);
	}

	@SuppressWarnings("unchecked")
	private List<ResourceModel> getResourceInfo(String appId, String version, File warFile, String resourcePath) {
		return ApplicationService.getResourceInfo(appId, version, warFile, resourcePath);
	}
	
	 
	public String actionForModify() throws SQLException { 
		String appId = this.getRequest().getParameter("appId");
		String sql = "select * from EASI_APP_DS where APP_ID = ?";
		List<AppdsModel> appDsList  = this.getConsoleQuery().queryForList(sql, new Object[]{appId},new AppdsRowMapper());
		sql = "select * from EASI_DS_INFO where SYS_DS_NAME <> 'default-ds'";
		List<DatasourceModel> dslist = this.getConsoleQuery().queryForList(sql, null, new DatasourceRowMapper());
		this.getRequest().setAttribute("dslist", dslist);
		this.getRequest().setAttribute("appDsList", appDsList);
		this.getRequest().setAttribute("_page", "/pages/application/modify.jsp");
		return "/pages/home.jsp";
	}

	
	
	public String actionForUpgrade()  throws SQLException {
		this.getRequest().setAttribute("_page", "/pages/application/upgrade.jsp");
		return "/pages/home.jsp";
	}
	
	private  boolean initAppDbscript(String appId,File jarFile) throws Exception{
		return ApplicationService.initAppDbscript(appId, jarFile);
	}
		
	/**
	 * 修改
	 * @param req
	 * @param resp
	 * @throws Exception 
	 */
	public EasyResult actionForUpdateApp()throws Exception{
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject jsonObject = this.getJSONObject();
		String appId = jsonObject.getString("appId");
		if(appId == null || "".equals(appId)){
			return EasyResult.error(0,"更新应用失败，原因：无效的应用参数[APP_ID]");
		}
		try{
			String sql = "select * from EASI_APP_DS where APP_ID = ? ";
			List<Map<String, String>> appDsList  = this.getConsoleQuery().queryForList(sql, new Object[]{appId},new MapRowMapperImpl());
			if(appDsList!=null&&appDsList.size()>0){
				sql = "update  EASI_APP_DS  set SYS_DS_NAME =? where APP_ID = ? and DS_NAME = ? ";
				for (int i = 0; i < appDsList.size(); i++) {
					Object[] params = new Object[]{jsonObject.getString("dsKey_"+(i+1)),appId,appDsList.get(i).get("DS_NAME")};
					this.getConsoleQuery().execute(sql, params);
				}
			}
			return EasyResult.ok(null,"保存成功");
		} catch (SQLException ex) {
			return EasyResult.error(0,"保存失败");
		}finally{
			AppContext.getContext(appId,true);
		}

	}
	
	public EasyResult actionForUpdateConfigRow(){
		JSONObject jsonObject = this.getJSONObject();
		String appId = jsonObject.getString("appId");
		if(appId == null || "".equals(appId)){
			return EasyResult.error(501,"更新应用失败，原因：无效的应用参数[APP_ID]");
		}
		if(!checkOperateTime().isOk()&&!Constants.EASITLINE_CONSOLE.equals(appId)) {
			return checkOperateTime();
		}
		String itemKey = jsonObject.getString("itemKey");
		String itemValue = jsonObject.getString("itemValue");
		String beforeItemValue = jsonObject.getString("beforeItemValue");
		long tempVal = System.currentTimeMillis();
		String sql = "update  EASI_APP_CONF  set ITEM_VALUE =?,UPDATE_TIME = ? where APP_ID = ? and ITEM_KEY = ? ";
		Object[] params = new Object[]{jsonObject.getString("itemValue"),EasyDate.getCurrentDateString(),appId,jsonObject.getString("itemKey")};
		try {
			int result = this.getConsoleQuery().executeUpdate(sql, params);
			if(result>0) {
				sql = "insert into EASI_APP_CONF_HIS(APP_ID,APP_VERSIOIN,BACKUP_TIME,ITEM_KEY,ITEM_VALUE) values(?,?,?,?,?)";
				params = new Object[] {appId,tempVal,EasyDate.getCurrentDateString(),jsonObject.getString("itemKey"),jsonObject.getString("itemValue")};
				this.getConsoleQuery().execute(sql, params);
				String msg="[应用配置变更] "+appId+", 参数["+itemKey+"]"+beforeItemValue+">"+itemValue;
				this.info(msg, null);
				this.addOperateLog(msg, JSONObject.toJSONString(jsonObject));
				return EasyResult.ok(null,"保存成功");
			}else {
				return EasyResult.error(501,"保存失败["+appId+"]["+itemKey+"]["+itemValue+"]");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.error(501,"保存失败");
		}
	}
	
	public EasyResult actionForUpdateConfig()throws Exception{
		JSONObject jsonObject = this.getJSONObject();
		String appId = jsonObject.getString("appId");
		if(appId == null || "".equals(appId)){
			return EasyResult.error(501,"更新应用失败，原因：无效的应用参数[APP_ID]");
		}
		if(!checkOperateTime().isOk()&&!Constants.EASITLINE_CONSOLE.equals(appId)) {
			return checkOperateTime();
		}
		
		try{
			String sql = "select * from EASI_APP_CONF  where APP_ID = ?";
			List<AppConfigModel> list = this.getConsoleQuery().queryForList(sql, new Object[]{appId},new AppConfigRowMapper());
			long tempVal=System.currentTimeMillis();
			for(AppConfigModel config:list){
				String value = StringUtils.trimToEmpty(jsonObject.getString("conf_"+config.getItemKey()));
				value = ConfigCryptorService.encryptString(value, "AV");
				sql = "update  EASI_APP_CONF  set ITEM_VALUE =? where APP_ID = ? and ITEM_KEY = ? ";
				Object[] params = new Object[]{value, config.getAppId(),config.getItemKey()};
				this.getConsoleQuery().execute(sql, params);
				
				sql = "insert into EASI_APP_CONF_HIS(APP_ID,APP_VERSIOIN,BACKUP_TIME,ITEM_KEY,ITEM_VALUE) values(?,?,?,?,?)";
			    params = new Object[] {appId,tempVal,EasyDate.getCurrentDateString(),config.getItemKey(),value};
			    this.getConsoleQuery().execute(sql, params);
			}
			
			this.addOperateLog("修改应用"+appId+"配置参数",JSONObject.toJSONString(jsonObject));
			
			this.getConsoleQuery().executeUpdate("UPDATE EASI_APP_INFO SET LAST_EDIT_CONF_TIME = ? where APP_ID = ?", EasyDate.getCurrentDateString(),appId);
			
			return EasyResult.ok(null,"保存成功");
	
		} catch (SQLException ex) {
			return EasyResult.error(501,"更新数据源失败，原因："+ex.getMessage());
		}finally{
			AppContext.getContext(appId,true);
		}

	}
	
	/**
	 * 删除
	 * @param req
	 * @param resp
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForUndeploy() throws Exception{ 
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject jsonObject = this.getJSONObject();
		String appId = jsonObject.getString("appId");
		String appWarName = jsonObject.getString("app.WAR_NAME");
		if(appId == null || "".equals(appId)){
			return EasyResult.error(0,"卸载应用失败，原因：无效的应用参数[APP_ID]");
		}
		Object[]  params = new Object[]{ appId 	};
		try {
			String appFilePath = this.getConsoleQuery().queryForString("select APP_FILE_PATH from EASI_APP_INFO where app_id = ?", params);
			
			String sql="delete from  EASI_APP_DS  where  APP_ID=?";
			this.getConsoleQuery().execute(sql, params);
			
		    sql="delete from  EASI_APP_DS_HIS  where  APP_ID=?";
			this.getConsoleQuery().execute(sql, params);
			
			sql="delete from  EASI_APP_INFO  where  APP_ID=?";
			this.getConsoleQuery().execute(sql, params);
			
			List<JSONObject> list=this.getConsoleQuery().queryForList("select * from EASI_APP_INFO_DEPLOY_HIS  where  APP_ID=?",params,new JSONMapperImpl());
			if(list!=null){
				for(JSONObject object:list){
					String path=object.getString("APP_FILE_PATH");
					File file=new File(path);
					if(file.exists())file.delete();
				}
			}
			
			sql="delete from  EASI_APP_INFO_DEPLOY_HIS  where  APP_ID=?";
			this.getConsoleQuery().execute(sql, params);
			
			if(appWarName.indexOf("jar")>-1) {
				File destFile = new java.io.File(appFilePath);
				if(destFile.exists()&&destFile.isFile())  destFile.delete(); 
			}else {
				File destFile = new java.io.File(deployDir() + File.separator + appWarName);
				if(destFile.exists()&&destFile.isFile())  destFile.delete(); 
				
				
				String deployedFileName = appWarName.replace(".war",".deployed");
				File deployedFile = new java.io.File(deployDir()  + File.separator + deployedFileName);
				if(deployedFile.exists()) {
					deployedFile.delete();
				} 
				
				File destFileDir = new java.io.File(deployDir() + File.separator + FileKit.getQianzui(appWarName));
				if(destFileDir.exists()&&destFileDir.isDirectory())destFileDir.delete(); 
			}
			
			this.addOperateLog("成功卸载应用["+appWarName+"]",JSONObject.toJSONString(jsonObject));
			
			return EasyResult.ok(null,"成功卸载应用");
		} catch (SQLException ex) {
			return EasyResult.error(0,"卸载应用失败，原因："+ex.getMessage());
		}finally{
			AppContext.getContext(appId,true);
		}
	}
	
	public EasyResult actionForTableData(){
		String tableName = getPara("tableName");
		try {
			tableName = AesUtils.getInstance().decrypt(tableName);
			if(StringUtils.isBlank(tableName)){
				return EasyResult.fail();
			}
			if(!tableName.startsWith("tbl_")){
				return EasyResult.fail();
			}
			tableName = tableName.replaceAll("tbl_", "");
			return  EasyResult.ok(this.getConsoleQuery().queryForList("select * from "+tableName+"",new Object[]{},new JSONMapperImpl()));
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.fail();
	}
	
	public void actionForRestart(){
		try {
			if(!ServerContext.isLoginAuth()){
				renderText("不支持window服务重启.");
				return;
			}
			new Thread(new Runnable() {
				@Override
				public void run() {
					try {
						String linuxCmd= Globals.BASE_DIR+File.separator+"run.sh";
						Process process=Runtime.getRuntime().exec(linuxCmd);
						process.waitFor();
					} catch (Exception e) {
						Thread.currentThread().interrupt();
					}
				}
			}).start();
			renderHtml("正在重启,请稍等几分钟~");
		} catch (Exception e) {
			this.error("启动错误:"+e.getMessage(), e);
			renderHtml("启动错误:"+e.getMessage());
		}
	}
	
	public EasyResult actionForClearHisData(){
		String[] tables=new String[]{"EASI_APP_DS_HIS","EASI_APP_INFO_DEPLOY_HIS"};
		EasyQuery sqliteQuery = Constants.getDb();
		for(String tableName:tables){
			try {
				sqliteQuery.executeUpdate("delete from "+tableName+"");
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForMoveData(){
		String[] tables=new String[]{"EASI_APP_CONF","EASI_APP_CONF_HIS","EASI_APP_DS","EASI_APP_DS_HIS","EASI_APP_INFO","EASI_APP_INFO_DEPLOY_HIS","EASI_APP_JOB","EASI_CONF","EASI_DS_INFO","EASI_USER","EASI_VERSION"};
		try {
			actionForUpdateTables();
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		EasyQuery query;
		try {
			query = ServerContext.getConsoleQuery();
		} catch (Exception e2) {
			e2.printStackTrace();
			return EasyResult.fail("不需要迁移数据,"+e2.getMessage());
		}
		EasyQuery sqliteQuery=ServerContext.getSqliteQuery();
		if(sqliteQuery.getTypes()==DBTypes.DERBY){
			return EasyResult.fail("sqlite未初始化!");
		}
		int i=0;
		int j=0;
		for(String tableName:tables){
			try {
				List<EasyRow> list = null;
				try {
					list = query.queryForList("select * from "+tableName+"");
				} catch (Exception e1) {
					return EasyResult.fail(e1.getMessage());
				}
				sqliteQuery.executeUpdate("delete from "+tableName+"");
				for(EasyRow row:list){
					String[] cols=row.getColumnNames();
					EasyRecord record=new EasyRecord(tableName);
					for(String colName:cols){
						record.set(colName, row.getColumnValue(colName));
					}
					try {
						sqliteQuery.save(record);
						i++;
					} catch (Exception e) {
						System.out.println(e.getMessage());
						j++;
					}
				}
				
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		try {
			ServerContext.reload();
			EasyPool.getInstance().update(null);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return EasyResult.ok("成功迁移("+i+")条数据;失败："+j+"条;");
	}
	
	
	public void  actionForUpdateTables(){
		String type=getPara("type");
		EasyQuery query;
		String msg="";
		if("sqlite".equals(type)){
			query=ServerContext.getSqliteQuery();
			msg+= ScriptHelper.getService().excuteSqlUpdate(query);
		}else{
			try {
				query = ServerContext.getConsoleQuery();
				msg+= ScriptHelper.getService().excuteSqlUpdate(query);
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			msg+= "<br><hr>";
			try {
				query = ServerContext.getSqliteQuery();
				msg+=ScriptHelper.getService().excuteSqlUpdate(query);
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		}
		renderHtml("<h3>执行成功</h3><br>"+msg);
	}
	
	@Override
	protected String getResId() {
		return null;
	}
	
	private void updateAppResource(ApplicationModel appInfo,List<ResourceModel> appResources) throws Exception{
		ResUpdateService.updateAppResource(appInfo, appResources);
	}
}
