package org.easitline.console.service;

import org.easitline.common.core.context.ConfigContext;
import org.easitline.common.utils.string.StringUtils;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.utils.DESUtil;

import java.io.File;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class ConfigCryptorService {

	private static String localSecretKey = null;
	
	private static boolean cryptorFlag = false;
	
	public static String prefixFlag = "S01_";
	
	public static boolean checkState() {
		String cryptoUrl = System.getProperty("crypto.url", "").trim();
		if(StringUtils.isBlank(cryptoUrl)) {
			return true;
		}
		URL url;
		try {
			url = new URL(cryptoUrl);
		} catch (MalformedURLException e) {
			ConsoleUtils.getLogger().error(e);
			return true;
		}
        String baseUrl = url.getProtocol() + "://" + url.getHost();
        if (url.getPort() != -1) {
            baseUrl += ":" + url.getPort();
        }
        try {
            URL _url = new URL(baseUrl);
            HttpURLConnection connection = (HttpURLConnection) _url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);  // 连接超时 3 秒
            connection.setReadTimeout(5000);     // 读取超时 3 秒
            int responseCode = connection.getResponseCode();
            return responseCode == 200;
        } catch (Exception e) {
        	ConsoleUtils.getLogger().error(e);
            return false;
        }
	}
	
	public static String decryptString(String value,String source) {
		if(StringUtils.notBlank(value)) {
			if(cryptorFlag) {
				ConsoleUtils.getLogger().info("decryptString:"+value);
				if(value.startsWith(prefixFlag)) {
					value = value.replace(prefixFlag,"");
					String result = ConfigContext.decryptionStr(value,source);
					ConsoleUtils.getLogger().info("decryptString:"+value+">>"+result);
					if(!value.equals(result)) {
						return result;
					}
				}
			}
			if("DS".equals(source)) {
				value = value.replace("3DES_", "");
				return DESUtil.getInstance(getLocalSk()).decryptStr(value);
			}
		}
		return value;
	}
	
	
	public static String encryptString(String value,String source) {
		if(StringUtils.notBlank(value)) {
			ConsoleUtils.getLogger().info("encryptString:"+value);
			if(cryptorFlag&&!value.startsWith(prefixFlag)) {
				String result = ConfigContext.encryptionStr(value);
				ConsoleUtils.getLogger().info("encryptString:"+value+">>"+result);
				if(!result.equals(value)) {
					return prefixFlag+result;
				}
			}
			if("DS".equals(source)) {
				return "3DES_"+DESUtil.getInstance(getLocalSk()).encryptStr(value);
			}
		}
		return value;
	}
	
	public static String desDecryptStr(String encryptedValue) {
		if (encryptedValue != null && encryptedValue.startsWith("3DES_")) {
			encryptedValue = encryptedValue.replace("3DES_", "");
			return DESUtil.getInstance(getLocalSk()).decryptStr(encryptedValue);
		}
		return encryptedValue;
	}
	
	public static String desEncryptStr(String encryptedValue) {
		if (encryptedValue != null && encryptedValue.startsWith("3DES_")) {
			encryptedValue = encryptedValue.replace("3DES_", "");
			return DESUtil.getInstance(getLocalSk()).encryptStr(encryptedValue);
		}
		return encryptedValue;
	}
	
	public static String getLocalSk() {
		if(localSecretKey!=null) {
			return localSecretKey;
		}
	    synchronized (ConfigCryptorService.class) {
	        if (localSecretKey == null) {
	            localSecretKey = loadLocalSecretKey();
	        }
	    }
		return localSecretKey;
	}
	
	/**
	 * secrets.inline.key
	 * secrets.key.file 
	 * @return
	 */
    private static String loadLocalSecretKey() {
    	String sk = System.getProperty("secrets.inline.key","");
    	if(StringUtils.notBlank(sk)) {
    		ConsoleUtils.getLogger().info("成功加载secrets.inline.key" ,null);
    		return sk;
    	}
    	String keyPath = System.getProperty("secrets.key.file","");
    	if(StringUtils.notBlank(keyPath)) {
    		File file = new File(keyPath);
    		Path path = Paths.get(keyPath);
    		if (Files.exists(path)) {
    			 try {
    				String content = new String(Files.readAllBytes(path), StandardCharsets.UTF_8).replaceAll("\\s+", "").trim();
     				if (content.startsWith("\uFEFF")) {
 						content = content.substring(1);
 					}
     				ConsoleUtils.getLogger().info("secrets.inline.file>"+file.getAbsolutePath()+">"+content);
     				return content;
	            } catch (Exception e) {
	            	ConsoleUtils.getLogger().error("读取秘钥文件失败: " + keyPath, e);
	            }
    		}else {
    			ConsoleUtils.getLogger().error(file.getAbsolutePath()+" - 路径不存在");
    		}
    	}
    	return "";
    }
	
}
