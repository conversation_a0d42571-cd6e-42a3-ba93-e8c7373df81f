package org.easitline.console.listener;

import java.io.File;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.easitline.common.core.EasyEngine;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.console.base.Constants;
import org.easitline.console.dbscript.DBHelper;
import org.easitline.console.job.JobManager;
import org.easitline.console.job.QuartzSchedulerEngine;
import org.easitline.console.log.LogBackupServer;
import org.easitline.console.log.LogIntegrityChecker;
import org.easitline.console.log.LogServer;
import org.easitline.console.log.LogWatcher;
import org.easitline.console.monitor.MarsDSMonitor;
import org.easitline.console.monitor.MarsMonitor;
import org.easitline.console.mqclient.LogTopicBroker;
import org.easitline.console.service.CryptoDataUpdateService;
import org.easitline.console.service.TomcatWarWatcher;
import org.easitline.console.utils.PropKit;

import com.alibaba.fastjson.parser.ParserConfig;


/** 
 * 应用上下文监听器
 */
@WebListener
public class GlobalContextListener  implements ServletContextListener {
	
	public  static  boolean  runState = true;

	private static String logName = Constants.EASITLINE_CONSOLE;
	private TomcatWarWatcher warWatcher = null;  // 添加实例变量
	
	
	/**
     * war被卸载时触发,在这个方法里针对应用被卸载时候做一些处理操作。
     */
    public void contextDestroyed(ServletContextEvent arg)  { 
    	runState = false;
    	addAppLog("Easitline console destory");
    	LogEngine.getLogger(logName).info("Easitline console destory!");
    	
    	// 停止war包监控
    	if (warWatcher != null) {
    	    warWatcher.stop();
    	    LogEngine.getLogger(logName).info("TomcatWarWatcher stopped.");
    	}
    	
    	LogTopicBroker.close();
    	
	   if(Constants.fileTamperedCheck()) {
	      // 关闭日志完整性检查器
	      LogIntegrityChecker.destroy();
	      // 停止日志监控
	      LogWatcher.getInstance().stop();
	    }
    	
    	QuartzSchedulerEngine.stopSchedulerEngin();   //停止调度任务引擎
    	
    }
    
	/**
     * war应用加载时被触发，通常用于加载调度、数据源等。
     */
    public void contextInitialized(ServletContextEvent context)  {
    	try {
			DBHelper.initPool();
			addAppLog("Easitline console init");
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error("Init mars server error,cause:"+ex.getMessage(),ex);
		}
    	
    	CryptoDataUpdateService.migrateEncryptedConfig();
    	
    	try {
    		EasyEngine engine = new EasyEngine();
        	engine.start();
		} catch (Exception ex) {
			System.out.println("Init mars server error,cause:"+ex.getMessage());
			LogEngine.getLogger(logName).error("Init mars server error,cause:"+ex.getMessage(),ex);
		}
    	LogEngine.getLogger(logName).info("================================================================================");
    	LogEngine.getLogger(logName).info("Welcome to Mars Dev Platform.");
    	LogEngine.getLogger(logName).info("Copyright (C) 2025, Inc. All rights reserved.");
    	LogEngine.getLogger(logName).info("https://gitee.com/easitline");
    	LogEngine.getLogger(logName).info("================================================================================");
    	
        Constants.serverBasePath = context.getServletContext().getRealPath("/");
      
        
      try {
        	PropKit.use("config.txt");
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error(ex);
		}
      
    	try {
    		LogEngine.getLogger(logName).info("Init mars dbscript ...");
        	DBHelper  helper = new DBHelper();
        	helper.run();  //执行数据库初始化操作，包括：初始化操作和版本更新操作，根据检测结果。     	
        	
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error(ex);
			addAppLog(ex.getMessage());
		}
    	
    	try {
        	String isJobServer = ServerContext.getProperties("G_JOB_SERVER", "false");
    		if("true".equals(isJobServer)){
    			QuartzSchedulerEngine.stopSchedulerEngin();
    			QuartzSchedulerEngine.startSchedulerEngin();   //启动调度任务引擎
            	JobManager jobService = new JobManager();
            	jobService.loadAndStartAllJobs();  //启动所有job
    		}
        } catch(Exception e) {
        	LogEngine.getLogger(logName).error(e.getMessage(),e);
        }
    	
    	
//    	String esUrl  = ServerContext.getProperties("ES_URL", "");
//    	if(StringUtils.isNotBlank(esUrl)) {
//    		try {
//				ESManager.getInstance().init();
//			} catch (Exception e) {
//				LogEngine.getLogger(logName).error(e.getMessage(),e);
//			}
//    	}
    	
        
        if(Constants.fileTamperedCheck()) {
        	// 初始化并启动日志监控
        	LogIntegrityChecker.init();
        	LogWatcher.getInstance().init();
        	
        }
    	
    	String bakLogFlag = ServerContext.getProperties("bakLogFlag", "0");
    	if("1".equals(bakLogFlag)) {
    		try {
    			Thread logBackupServer = new Thread(new LogBackupServer());
    			logBackupServer.start();
    		} catch (Exception ex) {
    			LogEngine.getLogger(logName).error(ex.getMessage(),ex);
    		}
    	}
    	
    	String marsMonitor = AppContext.getContext(Constants.EASITLINE_CONSOLE).getProperty("marsMonitor", "0");
    	if("1".equals(marsMonitor)) {
            try {
            	LogEngine.getLogger(logName).info("[MarsDSMonitor] Start...");
            	Thread dsMonitorThread = new Thread(new MarsDSMonitor());
            	dsMonitorThread.start();
            } catch (Exception ex) {
            	LogEngine.getLogger(logName).error(ex.getMessage(),ex);
            }
            
	    	try {
	    		Thread logServer = new Thread(new LogServer());
	    		logServer.start();
	    	} catch (Exception ex) {
	    		LogEngine.getLogger(logName).error(ex.getMessage(),ex);
	    	}
	    		
	        try {
	        	Thread monitorThread = new Thread(new MarsMonitor());
	        	monitorThread.start();
	        } catch (Exception ex) {
	        	LogEngine.getLogger(logName).error(ex.getMessage(),ex);
	        }
    	}
        
        //自动检测要部署的war
       this.autoDeploy();
        
       ParserConfig.getGlobalInstance().setAutoTypeSupport(false);
       ParserConfig.getGlobalInstance().setAsmEnable(false);
       ParserConfig.getGlobalInstance().setSafeMode(true);
    }
    
    protected void addAppLog(String msg) {
		EasyRecord record = new EasyRecord("EASI_APP_LOG","LOG_ID");
		record.setPrimaryValues(RandomKit.uuid());
		record.set("APP_ID", Constants.EASITLINE_CONSOLE);
		record.set("MSG", msg);
		record.set("OPERATE_TIME", EasyDate.getCurrentDateString());
		record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		try {
			LogEngine.getLogger(logName).info(msg);
			 Constants.getDb().save(record);
		} catch (Exception e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
		}
		
	}

    
    
    private void autoDeploy() {
    	String warAutoDeploy = AppContext.getContext(Constants.EASITLINE_CONSOLE).getProperty("warAutoDeploy", "0");
    	if("1".equals(warAutoDeploy)) {
    		try {
    			String profileActive  = ServerContext.getProperties("PROFILE_ACTIVE", "");
    			if("test".equals(profileActive)||"dev".equals(profileActive)) {
    				String tomcatWebappsPath = AppContext.getContext(Constants.EASITLINE_CONSOLE).getProperty("autoDeployDir",Globals.SERVER_DIR+File.separator+"autoDeploy");
    				warWatcher = new TomcatWarWatcher(tomcatWebappsPath);  // 保存实例引用
    				Thread watcherThread = new Thread(warWatcher, "TomcatWarWatcher");
    				watcherThread.setDaemon(true);
    				watcherThread.start();
    				LogEngine.getLogger(logName).info("[TomcatWarWatcher] Started watching directory: " + tomcatWebappsPath);
    			}
    		} catch (Exception e) {
    			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
    		}
    	}
    }

}
