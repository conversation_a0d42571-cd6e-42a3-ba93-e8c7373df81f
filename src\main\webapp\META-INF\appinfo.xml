<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="easitline-console" name="应用管理中心" package-by="46419" package-time="2025-08-04 15:27:38" version="3.5#20250804-1">
	<description>
    </description>
	<versionHistory>
		3.4#20241118-1  1、新增druid配置connectTimeout和socketTimeout
		3.4#20240328-1	1、优化系统数据源的测试，防止数据源链接未被使用导致被释放。
		3.4#20240104-1	1、增加更新菜单剔除字段配置
		3.4#20231122-1	1、增加OSS配置
		3.4#20230901-1	1、增加console安全，关键信息加密传输
		3.2#20230221-1	1、增加对apusic应用部署的支持。
		3.2#20220903-1	1、MQ类型，增加对CTGMQ类型的配置支持。
		3.2#20220701-1	1、在日志配置界面，增加MQ采集日志的配置项，如果打开，则把平台的日志通过MQ发送到ES，提供统一的检索。
	    3.2#20220512-1	1、支持日志备份功能，把tomcat的主日志备份到配置目录，缺省为easyserver/logbak目录。  
		3.2#20220328-1	1、增加mars日志收集服务，主要用于收集在线、话务及接口相关的系统日志。
		3.2#20220323-1	1、增加mars日志备份功能。
		3.2#20220114-1	1、解决mars不输出tomcat日志的问题。
    	3.2#20220113-1	1、增加磐石监控平台的数据采集入口功能  2、提供mars的统一告警服务  3、增加对SQL、连接池、CPU、内存、磁盘的告警实现。
	    3.2#20211221-1 	1、修正对cpu空闲率判断不对的问题。
    </versionHistory>
</application>
